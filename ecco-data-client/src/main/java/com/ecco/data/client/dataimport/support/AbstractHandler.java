package com.ecco.data.client.dataimport.support;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;

import com.ecco.data.client.WebApiSettings;
import com.ecco.data.client.actors.*;
import com.ecco.infrastructure.rest.RestClient;
import com.ecco.webApi.contacts.AgencyViewModel;
import com.ecco.webApi.contacts.IndividualViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.viewModels.Result;
import com.google.common.base.Charsets;
import org.apache.commons.lang3.StringUtils;
import org.jspecify.annotations.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.util.Assert;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import com.ecco.infrastructure.config.web.ConvertersConfig;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.ecco.webApi.evidence.BaseCommandViewModel;

import static org.springframework.util.StringUtils.hasText;

public abstract class AbstractHandler<T> implements Function<ImportOperation<T>, Void> {

    protected final Logger log = LoggerFactory.getLogger(getClass());
    protected static final String apiPath = "/api/";

    private final RestTemplate restTemplate;
    protected final BaseCommandActor commandActor;
    protected final ClientActor clientActor;
    protected final ReferralActor referralActor;
    protected final ReportActor reportActor;
    protected final ServiceActor serviceActor;
    protected final WorkerActor workerActor;
    protected final ContactActor contactActor;
    protected final AgreementActor agreementActor;
    protected final ListDefActor listDefActor;
    protected final SessionDataActor sessionDataActor;
    protected final CacheActor cacheActor;

    public AbstractHandler(RestTemplate restTemplate) {
        addWebApiConverters(restTemplate);
        this.restTemplate = restTemplate;
        // ensure the actors pick up the command line argument 'target.host.url'
        this.commandActor = new BaseActor(restTemplate);
        this.clientActor = new ClientActor(restTemplate);
        this.referralActor = new ReferralActor(restTemplate);
        this.reportActor = new ReportActor(restTemplate);
        this.serviceActor = new ServiceActor(restTemplate);
        this.workerActor = new WorkerActor(restTemplate);
        this.contactActor = new ContactActor(restTemplate);
        this.agreementActor = new AgreementActor(restTemplate);
        this.listDefActor = new ListDefActor(restTemplate);
        this.sessionDataActor = new SessionDataActor(restTemplate);
        this.cacheActor = new CacheActor(restTemplate);
    }

    private void addWebApiConverters(RestTemplate restTemplate) {
        List<HttpMessageConverter<?>> messageConverterList = new ArrayList<>();
        ConvertersConfig.addWebApiConvertersTo(messageConverterList);
        restTemplate.setMessageConverters(messageConverterList);
    }

    /**
     * Submit this entity using one or more {@link #postAcceptingCreatedOrUnprocessableEntity(String, Object)}
     * calls.
     * The entity should be updated with the resulting ids generated
     */
    protected abstract void processEntity(ImportOperation<T> entity);

    protected void processBatchedEntities(List<ImportOperation<T>> entity) {}

    public Void apply(List<ImportOperation<T>> beans) {
        verifyUrlMatches(beans);

        for (ImportOperation<T> bean : beans) {
            log.info("row #{} batching", bean.row);
        }

        int firstRow = beans.get(0).row;
        int lastRow = beans.get(beans.size() - 1).row;
        try {
            processBatchedEntities(beans);
            log.info("rows #{} to #{} imported", firstRow, lastRow);
        } catch (HttpServerErrorException e) {
            log.error(e.getMessage());
            log.info("rows #{} to #{}. Response was:" + e.getResponseBodyAsString(), firstRow, lastRow);
        } catch (RestClientException e) {
            log.info("rows #{} to #{}. Exception:" + e.getMessage(), firstRow, lastRow);
            System.err.println("Exception: " + e.getMessage());
        }
        return null;
    }

    /**
     * This is the touch-point between the BaseCommandActor which uses cmd-line arg 'target.host.url'
     * and the csv files constructed beans to be processed, which also have a url.
     * Therefore we check that both URL's match so that we help prevent mistakes between mismatched urls
     * and ensures a double-check on the config in the csv and the cmd-line.
     */
    private void verifyUrlMatches(List<ImportOperation<T>> beans) {
        for (ImportOperation<T> bean : beans) {
            if (bean != null) {
                // We need to allow for test scenarios, eg ImportProcessorTest or CI on GitHub Actions, where the url may be completely
                // different to the fixed url in the csv's. So if its localhost ecco-war then ignore the check - and assume
                // we're not using localhost in a tunnel or on servers.
                // NB WebApiSettings.APPLICATION_URL is what is consumed in BaseCommandActor (which is also passed RestClient.template from ImportProcessorDefaultImpl)
                // TODO Remove this isLikelyTest by supplying RestClient to the actors so that getApiBaseUrl below can defer to RestClient
                boolean isLikelyTest = WebApiSettings.APPLICATION_URL.contains("localhost") && WebApiSettings.APPLICATION_URL.contains("ecco-war");
                if (!isLikelyTest) {
                    String apiRelativePath = "/" + RestClient.apiPath + "/";
                    Assert.isTrue(StringUtils.equalsIgnoreCase(bean.baseUri + apiRelativePath, this.commandActor.getApiBaseUrl()),
                            "URLs in target.host.url and csv file do not match: " + bean.baseUri + apiRelativePath + " vs " + this.commandActor.getApiBaseUrl());
                }
            }
        }
    }

    @Override
    public Void apply(ImportOperation<T> input) {
        verifyUrlMatches(Collections.singletonList(input));
        try {
            processEntity(input);
            log.info("row #{} imported", input.row);
        } catch (HttpServerErrorException e) {
            log.error(e.getMessage());
            log.error("row #{}.  Response was: " + e.getResponseBodyAsString(), input.row);
        } catch (RestClientException e) {
            log.error("row #{}.  Exception: " + e.getMessage(), input.row);
            System.err.println("Exception: " + e.getMessage());
        }
        return null;
    }

    protected <R> ResponseEntity<R> getForEntity(String urlWithTemplateParams, Class<R> responseType, Object... urlVariables) {
        return restTemplate.getForEntity(urlWithTemplateParams, responseType, urlVariables);
    }

    /**
     * When a POST is required (manipulates data) and yet we don't return an Entity
     * Also see *Actor classes for other use cases
     */
    protected void postForOK(String url) {
        final HttpHeaders postHeaders = new HttpHeaders();
        postHeaders.setContentType(MediaType.APPLICATION_JSON);
        final HttpEntity<?> postRequest = new HttpEntity<>(null, postHeaders);
        ResponseEntity<Result> result = restTemplate.postForEntity(url, postRequest, Result.class);
        Assert.state(result.getStatusCode() == HttpStatus.CREATED,
                result.getBody().getMessage());
    }

    /**
     * Post the entity, and accept either 201 CREATED, or 422 UNPROCESSABLE_ENTITY, which is currently the best
     * we've come up with to indicate that the POST was not accepted, but returns the id of the entity
     * that already exists matching the <code>code</code> field of the entity.
     *
     * @return Result containing the allocated id, and possibly a message
     */
    protected <E> Result postAcceptingCreatedOrUnprocessableEntity(String url, E entity) {
        ResponseEntity<Result> result = restTemplate.postForEntity(url, entity, Result.class);

        Assert.state(result.getStatusCode() == HttpStatus.CREATED
                || result.getStatusCode() == HttpStatus.UNPROCESSABLE_ENTITY,
                result.getBody().getMessage());

        log.debug("POST " + url + " returned id = " + result.getBody().getId());
        return result.getBody();
    }

    /**
     * Executes the command, asserting the command was (or had previously been) successful.
     * @return The webApi Result which is gathered from {@link BaseCommandHandler#handleCommand(org.springframework.security.core.Authentication, Object, String)}
     */
    protected com.ecco.webApi.viewModels.Result executeCommand(String baseUrl, BaseCommandViewModel vm) {
        ResponseEntity<com.ecco.webApi.viewModels.Result> result = commandActor.executeCommand(baseUrl, vm);

        log.debug("POST " + baseUrl + " returned id = " + result.getBody().getId());
        return result.getBody();
    }

    /**
     * Put the entity, and accept 200 OK
     */
    protected <E> void putEntity(String url, E entity) {

        // we could return a Result if we used exchange method
        //ResponseEntity<Result> result = rest.exchange(url, HttpMethod.PUT, requestEntity, Result.class);
        restTemplate.put(url, entity);

        log.debug("PUT " + url);
    }

    /** return id, or throw an exception */
    protected long syncIndividualToServer(String uriBase, IndividualViewModel input, boolean strictReferenceData) {

        // check we have the organisation saved
        if (input.organisation != null && (hasText(input.organisation.code) || hasText(input.organisation.companyName))) {

            if (strictReferenceData) {
                //Assert.notNull(input.organisation.agencyCategoryId, "must assign a category");

                // if not strictReferenceData then we can set a helpful assumption
            } else {
                /*if (input.organisation.agencyCategoryId == null) {
                    input.organisation.agencyCategory = "unknown";
                }*/
            }

            input.organisationId = syncAgencyToServer(uriBase, input.organisation);
        }

        // its helpful to assign a code to all contacts so future lookups find the same one
        // otherwise we end up with duplicates within organisations
        // so the business logic we use is if they are the same name, and organisation
        if (input.code == null) {
            input.code = input.createUniqueEnoughCode();
        }

        Result result = postAcceptingCreatedOrUnprocessableEntity(uriBase + apiPath + "individuals/", input);
        return Long.parseLong(result.getId());
    }

    protected long syncAgencyToServer(String uriBase, AgencyViewModel input) {

        // first try to find a match to avoid rejection when count>1 causing postAcceptingCreatedOrUnprocessableEntity
        // which in AgencyController.create - determined when finding multiples by code, then multiples by companyName
        Long agencyId = findExistingAgency(uriBase, input);
        if (agencyId != null) return agencyId;

        // failing the matches, try to create
        Result result = postAcceptingCreatedOrUnprocessableEntity(uriBase + apiPath + "agencies/", input);
        return Long.parseLong(result.getId());
    }

    private Long findExistingAgency(String uriBase, AgencyViewModel input) {

        // find by code
        if (StringUtils.isNotBlank(input.getCode())) {
            ResponseEntity<AgencyViewModel[]> agenciesByCode = getForEntity(uriBase + apiPath +  "agencies/byCode/{code}/", AgencyViewModel[].class, input.getCode());
            if (agenciesByCode != null) {
                // only if there is an exact match
                // if there are more agencies with a code, this might actually represent a problem
                // since code's are only typically set by data import
                if (agenciesByCode.getBody().length == 1) {
                    return agenciesByCode.getBody()[0].contactId;
                }
            }
        }

        // find by company name
        // this time we use the first match - because we really don't care if there are duplicates, we'd rather not fail the import
        if (StringUtils.isNotBlank(input.getCompanyName())) {
            ResponseEntity<AgencyViewModel[]> agencies = getForEntity(uriBase + apiPath + "agencies/byCompanyName/{companyNamePrefix}/", AgencyViewModel[].class, input.getCompanyName());
            if (agencies != null) {
                if (agencies.getBody().length > 0) {
                    return agencies.getBody()[0].contactId;
                }
            }
        }

        return null;
    }

    @NonNull
    protected ReferralViewModel getReferralFromSrId(ImportOperation<T> operation, @NonNull Integer srId) {
        // lookup the id from the code
        ResponseEntity<ReferralViewModel> response = getReferralBySrId(operation.baseUri + apiPath, srId);
        assert response != null;
        ReferralViewModel rvm = response.getBody();

        assert rvm != null;
        Assert.notNull(rvm.getReferralId(), "referralId missing");
        return rvm;
    }

    @NonNull
    protected ReferralViewModel getReferralFromCode(ImportOperation<T> operation, @NonNull String referralCode) {
        // lookup the id from the code
        ResponseEntity<ReferralViewModel> response = getReferralByCode(operation.baseUri + apiPath, referralCode);
        assert response != null;
        ReferralViewModel rvm = response.getBody();

        assert rvm != null;
        Assert.notNull(rvm.getReferralId(), "referralId missing");
        return rvm;
    }

    /*@NonNull
    protected ReferralSummaryViewModel getFirstReferralFromLine1(ImportOperation<T> operation, @NonNull String line1) {
        Iterable<ClientViewModel> response = clientActor.getClientsByAddressLine1(operation.baseUri + apiPath, line1);
        assert response != null;

        // make it an exact match
        var cvm = StreamSupport.stream(response.spliterator(), false)
                .filter(c -> c.address.getAddress()[0].equals(line1))
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("no client found with address line1: " + line1));
        assert cvm != null;

        ResponseEntity<ReferralSummaryViewModel[]> referrals = referralActor.getReferralsByClientIdWithoutAclSecurity(cvm.clientId);
        assert referrals != null;
        Assert.notNull(referrals.getBody()[0], "referral missing");
        return referrals.getBody()[0];
    }*/

    private ResponseEntity<ReferralViewModel> getReferralByCode(String url, String code) {
        return getForEntity(url + "referrals/byCode/{code}/", ReferralViewModel.class, URLEncoder.encode(code, Charsets.UTF_8));
    }

    private ResponseEntity<ReferralViewModel> getReferralBySrId(String url, Integer srId) {
        return getForEntity(url + "referrals/byServiceRecipient/{srId}/", ReferralViewModel.class, srId);
    }
}
