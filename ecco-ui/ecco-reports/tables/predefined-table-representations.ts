///<reference path="../../../ecco-offline/src/main/resources/com/ecco/offline/staticFiles/scripts/typings/lazy.d.ts"/>

import {applicationRootPath} from "application-properties";
import Analysers = require("../Analysers");
import smartStepCount = require("../analysis/smartStepCount");
import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import {fullAddress, ServiceRecipient, SessionData, streetAddress, SupportWork, BaseServiceRecipientCommandDto} from "ecco-dto";
import {
    EccoDate,
    EccoDateTime,
    StringToStringMap,
    StringUtils
} from "@eccosolutions/ecco-common";
import {AppointmentDto} from "ecco-dto";
import {FormEvidence} from "ecco-dto";
import {ReferralDto, ReferralSummaryDto} from "ecco-dto";
import {StaffDto} from "ecco-dto";
import {AgreementDto, ServiceRecipientDemandDto} from "ecco-rota";
import {Client} from "ecco-dto";
import {Agency, Individual} from "ecco-dto";
import * as evidenceDto from "ecco-dto";
import * as groupSupportDtos from "ecco-dto";
import {
    representation,
    HrefData,
    joinColumnMaps,
    joinNestedPathColumnMaps,
    columnMap,
    booleanColumn,
    numberColumn,
    textColumn,
    dateColumn,
    dateTimeColumn,
    fixedPrecisionNumberColumn,
    hrefColumn
} from "../controls/tableSupport";
import {getServiceAllocation} from "../analysis/referralCommonAnalysis";
import {getGlobalEccoAPI} from "ecco-components";
import {EntityWithParent, extractPair, Group} from "../analysis/types";
import {commandOnlyColumns} from "../analysis/commandAnalysis";

//**********************
// Generalised functions

export function ensureEntityReferencesParent<P, C>(entity: C, parent: P): EntityWithParent<P, C> {
    // TS2322: Type '{ parent: P; }' is not assignable to type 'EntityWithParent<P, C>'.
    // Type '{ parent: P; }' is not assignable to type 'C'.
    // 'C' could be instantiated with an arbitrary type which could be unrelated to '{ parent: P; }'.
    // let result: EntityWithParent<P, C> = entity && {
    //     parent: parent
    // };

    // simpler
    // @ts-ignore
    entity["parent"] = parent;
    return entity as EntityWithParent<P, C>;
}
/*function flattenWithParentByProperty<P, C>(input: Sequence<P>, propertyArray: keyof P): Sequence<EntityWithParent<P, C>> {
    return input
        .map(parent => ensureEntityReferencesParent(parent[propertyArray], parent))
        .flatten<EntityWithParent<P, C>>();
}*/
/** @Exemplar of using parent */
export function flattenWithParentByFn<P, C>(
    input: Sequence<P>,
    getChildArray: (parent: P) => C[]
): Sequence<EntityWithParent<P, C>> {
    return input
        .filter(parent => parent != null)
        .map(parent => {
            return {parent, children: getChildArray(parent)};
        })
        .filter(pc => pc.children != null)
        .map(pc => pc.children.map(c => ensureEntityReferencesParent(c, pc.parent)))
        .flatten<C>()
        .flatten<EntityWithParent<P, C>>();
}

export function countsBy<T>(
    input: Sequence<T>,
    extractKey: (input: T) => string
): Sequence<Group<T>> {
    return input
        .groupBy(inputElement => extractKey(inputElement))
        .pairs()
        .map(extractPair)
        .map(pair => {
            return {
                key: pair.key,
                count: pair.elements.size(),
                elements: pair.elements
            };
        });
}


export function outstandingSmartSteps(supportWork: LazyJS.Sequence<SupportWork>) {
    const allActionsWithWork = smartStepCount.smartStepCountsFromSupportWork(supportWork);
    const count = new smartStepCount.SmartStepCount(allActionsWithWork);
    return count.getTotalOutstandingSmartSteps();
}

export function achievedSmartSteps(supportWork: LazyJS.Sequence<SupportWork>) {
    const allActionsWithWork = smartStepCount.smartStepCountsFromSupportWork(supportWork);
    const count = new smartStepCount.SmartStepCount(allActionsWithWork);
    return count.getTotalAchievedSmartSteps();
}

// used, for example, for 'status now'
export function messageLookup(key: string): string {
    const messages = getGlobalEccoAPI().sessionData.getMessages();
    // @ts-ignore
    return messages[key] || "[" + key + "]";
}

// this used to just return the 'referralView.' message - see ui-messages.properties (and messages table),
// or simply pass back the input, but its only used for taskName so we call the common lookupTaskName
export function evidenceTaskNameLookup(
    taskName: string | null,
    serviceAllocationId: number | null
): string {
    if (!taskName && !serviceAllocationId) {
        return "";
    }
    const sessionData = getGlobalEccoAPI().sessionData;
    // as per CommandHistoryItemControl.getTitleForCommand()
    if (serviceAllocationId != null && taskName != null) {
        const st = sessionData.getServiceTypeFromServiceCategorisation(serviceAllocationId);
        // titleRaw then referralView. then referralBreadcrumb then camel to spaces
        return st.lookupTaskName(taskName) || "";
    } else {
        return StringUtils.camelToSpaces(taskName || "");
    }
}
export function actionDefIdLookup(actionDefId: number, sessionData: SessionData): string {
    return sessionData.getAnyActionById(actionDefId).getName();
}
export function questionDefIdLookup(questionDefId: number, sessionData: SessionData): string {
    return sessionData.getQuestionById(questionDefId).name;
}
export function questionGroupDefByQuestionIdLookup(
    questionDefId: number,
    sessionData: SessionData
): string {
    return sessionData.getQuestionGroupByQuestionId(questionDefId)!.name;
}
export function questionAnswerDisplayValueLookup(
    questionDefId: number,
    answerValue: string | undefined,
    sessionData: SessionData
): string {
    return sessionData.getAnswerDisplayValue(questionDefId, answerValue);
}
export function listDefIdLookup(key: number | null, sessionData: SessionData): string | null {
    if (key == null) {
        return null;
    }
    return sessionData.getListDefinitionEntryById(key).getName();
}


/**
 * Only applicable for accepted clients ('closed' have to have been accepted) - rejected have no length on service.
 * Does use today's date as the current length of service where no exited exists
 */
export function lengthOfDaysOnService(item: Analysers.ReferralReportItem): number | null {
    const endDteStr = item.referral.exitedDate;
    const startDteStr = item.referral.receivingServiceDate
        ? item.referral.receivingServiceDate
        : item.referral.decisionMadeOn;
    if (!startDteStr) {
        return null; // no accepted/start date;
    }
    const endDte = endDteStr ? EccoDate.parseIso8601(endDteStr) : EccoDate.todayUtc();
    const startDte = EccoDate.parseIso8601(startDteStr);
    const diff = endDte!.toUtcJsDate().getTime() - startDte!.toUtcJsDate().getTime();
    return diff / (1000 * 3600 * 24);
}
export function lengthOfDaysOnWaiting(item: Analysers.ReferralReportItem): number | null {
    // see ReferralStatusCommonPredicates
    // waiting is decisionMadeOn to receivingServiceDate
    const startDteStr = item.referral.decisionMadeOn;
    const endDteStr = item.referral.receivingServiceDate;
    if (!startDteStr) {
        return null; // no decision date - we can't do a calc
    }
    const startDte = EccoDate.parseIso8601(startDteStr);
    const endDte = endDteStr ? EccoDate.parseIso8601(endDteStr) : EccoDate.todayUtc();
    const diff = endDte.toUtcJsDate().getTime() - startDte.toUtcJsDate().getTime();
    return diff / (1000 * 3600 * 24);
}

function firstWorkDate(
    workItemsIn: Sequence<evidenceDto.BaseWork>,
    taskName?: string
): string | null {
    // findQuestionnaireWorkByServiceRecipientId calls QuestionnaireEvidenceController
    // which returns OrderbyWorkDateDescCreatedDesc
    // therefore the first is the latest - so go with the last

    // filter by task name first
    let workItems = taskName
        ? workItemsIn.filter((w: evidenceDto.BaseWork) => w.taskName == taskName)
        : workItemsIn;

    const lastWorkItem = workItems.last();

    if (!lastWorkItem) {
        return null; // no work
    }
    return lastWorkItem.workDate;
}

export function daysBetween(startDteStr: string | null, endDteStr: string | null): number | null {
    if (!startDteStr || !endDteStr) {
        return null;
    }
    const endDte =
        EccoDateTime.parseIso8601(endDteStr) == null
            ? EccoDate.parseIso8601(endDteStr)
            : EccoDateTime.parseIso8601(endDteStr);
    const startDte =
        EccoDateTime.parseIso8601(startDteStr) == null
            ? EccoDate.parseIso8601(startDteStr)
            : EccoDateTime.parseIso8601(startDteStr);
    const diff = endDte.toUtcJsDate().getTime() - startDte.toUtcJsDate().getTime();
    return diff / (1000 * 3600 * 24);
}

/** Calculates the signposted date - see ReferralStatusCommonPredicate.java */
function signpostedDate(item: Analysers.ReferralReportItem): string | null {
    const signposted = item.referral.decisionMadeOn;
    return (item.referral.appropriateReferralState == "SIGNPOSTED" ||
        item.referral.acceptOnServiceState == "SIGNPOSTED") &&
        signposted
        ? signposted
        : null;
}

export function getAgeInYearsAtTimeOfReferral(item: Analysers.ReferralReportItem): number | null {
    if (!item.client!.birthDate) {
        return null;
    }
    if (!item.referral.receivedDate) {
        return null;
    }
    return getAgeInYearsBetweenDates(item.client!.birthDate, item.referral.receivedDate);
}

/**
 * Age at exit is only helpful for those who are not signposted
 */
export function getAgeInYearsAtTimeOfExit(item: Analysers.ReferralReportItem): number | null {
    if (!item.client!.birthDate) {
        return null;
    }
    if (!item.referral.exitedDate) {
        return null;
    }
    return getAgeInYearsBetweenDates(item.client!.birthDate, item.referral.exitedDate);
}

export function getAgeInYearsBetweenDates(fromStr: string | null, toStr: string): number | null {
    const fromDte = EccoDate.parseIso8601(fromStr);
    const toDte = EccoDate.parseIso8601(toStr);
    return getAgeInYearsBetweenEccoDates(fromDte, toDte);
}

export function getAgeInYearsBetweenEccoDates(
    fromDte: EccoDate | null,
    toDte: EccoDate | null
): number | null {
    if (fromDte == null || toDte == null) {
        return null;
    }
    let diff = toDte.toUtcJsDate().getTime() - fromDte.toUtcJsDate().getTime();
    diff = diff / (1000 * 3600 * 24 * 365.25); // stand a chance of dealing safely with leap-day births
    return Math.floor(diff);
}

export function countBits(n: number): number {
    let count = 0;
    while (n > 0) {
        if ((n & 1) == 1) count++;
        n >>= 1;
    }
    return count;
}


/** daysAfterSun is zero-indexed day of week (Sun = 0, Sat = 6) */
function isOnDayOfWeek(
    involvement: groupSupportDtos.ClientAttendanceDto,
    daysAfterSun: number
): boolean {
    const date = EccoDateTime.parseIso8601Utc(
        involvement.parentActivity!.startDateTime
    ).toEccoDate();
    return date.getDayOfWeek() == daysAfterSun;
}

function isDateOnDayOfWeek(dateStr: string, daysAfterSun: number): boolean {
    const date = EccoDate.parseIso8601(dateStr);
    return date.getDayOfWeek() == daysAfterSun;
}

function attendancesOnDay(
    involvement: groupSupportDtos.ClientAttendanceDto,
    daysAfterSunday: number
): number {
    if (!involvement.parentActivity!.endDate) {
        // single day activity, use this approach
        return involvement.attended && isOnDayOfWeek(involvement, daysAfterSunday) ? 1 : 0;
    }
    // for multi-day, count from the register
    return involvement.dailyAttendances.filter(
        attendance =>
            attendance.attendedAllDay && isDateOnDayOfWeek(attendance.date, daysAfterSunday)
    ).length;
}

/** Total actual attendances in this involvement */
function actualAttendances(involvement: groupSupportDtos.ClientAttendanceDto): number {
    if (!involvement.parentActivity!.endDate) {
        // single day activity, use this approach
        return involvement.attended ? 1 : 0;
    }
    // for multi-day, count from the register
    return involvement.dailyAttendances.filter(attendance => attendance.attendedAllDay).length;
}

/** Total expected attendances in this involvement */
function expectedAttendances(involvement: groupSupportDtos.ClientAttendanceDto): number {
    if (!involvement.parentActivity!.endDate) {
        // single day activity, use this approach
        return involvement.attending ? 1 : 0;
    }
    // for multi-day, count from the register
    return involvement.dailyAttendances.filter(attendance => attendance.attending).length;
}

/** Collection of values that can be shown as a column (or otherwise) of a ReferralReportItem */
export enum EntityType {
    Client,
    Referral,
    SupportWork,
    ServiceType,
    Building
}
export function entityUrl(id: string, type: EntityType, secondId?: string): string | null {
    if (id == null) {
        return null;
    }

    switch (type) {
        case EntityType.SupportWork:
            return `${applicationRootPath}support-history/svcrec/${secondId}?workUuid=${id}`;
        case EntityType.ServiceType:
            return `${applicationRootPath}service-config/servicetype/${id}/`;
        default:
            //return URI(referralPrefix).segmentCoded(id.toString()).absoluteTo(applicationProperties.applicationRootPath);
            //const referralPrefix = "/nav/referrals/beta/";
            // also see ClientReferralsPopup#openReferral
            return `${applicationRootPath}referrals/${id}/`;
    }
}
const referralHref: (referral: ReferralDto | ReferralSummaryDto) => HrefData = referral => {
    const hrefData: HrefData = {
        display: referral.referralCode || referral.referralId!.toString(),
        url: entityUrl(referral.referralId!.toString(), EntityType.Referral) || undefined
    };
    return hrefData;
};

export var referralAggregateReportItemColumns = columnMap(
    numberColumn<Analysers.ReferralReportItem>("sr-id", row => row.referral.serviceRecipientId),
    hrefColumn<Analysers.ReferralReportItem>("r-id", row => referralHref(row.referral)),
    numberColumn<Analysers.ReferralReportItem>("referralId", row => row.referral.referralId),
    textColumn<Analysers.ReferralReportItem>("r-code", row => row.referral.referralCode),
    textColumn<Analysers.ReferralReportItem>(
        "rid",
        row => row.referral.referralCode || row.referral.referralId.toString()
    ),
    numberColumn<Analysers.ReferralReportItem>(
        "primaryReferralId",
        row => row.referral.primaryReferralId
    ),
    textColumn<Analysers.ReferralReportItem>("primaryRelationship", row =>
        listDefIdLookup(row.referral.primaryRelationshipId || null, row.sessionData!)
    ),
    textColumn<Analysers.ReferralReportItem>(
        "c-id",
        row => row.referral.clientCode || row.referral.clientId.toString()
    ),
    textColumn<Analysers.ReferralReportItem>(
        "cid",
        row => row.referral.clientCode || row.referral.clientId.toString()
    ),
    numberColumn<Analysers.ReferralReportItem>("clientId", row => row.referral.clientId),
    textColumn<Analysers.ReferralReportItem>("c-code", row => row.referral.clientCode),
    textColumn<Analysers.ReferralReportItem>("client", row => row.referral.clientDisplayName),
    textColumn<Analysers.ReferralReportItem>("client status", row =>
        listDefIdLookup(row.referral.latestClientStatusId || null, row.sessionData!)
    ),
    dateTimeColumn<Analysers.ReferralReportItem>(
        "client status at",
        row =>
            (row.referral.latestClientStatusDateTime &&
                EccoDateTime.parseIso8601Utc(row.referral.latestClientStatusDateTime)) ||
            null
    ),
    textColumn<Analysers.ReferralReportItem>(
        "service",
        (row, ctx) =>
            ctx.getSessionData().getServiceCategorisation(row.referral.serviceAllocationId)
                .serviceName
    ),
    numberColumn<Analysers.ReferralReportItem>("sa-id", row => row.referral.serviceAllocationId),
    textColumn<Analysers.ReferralReportItem>("company", row => {
        const allocation = getServiceAllocation(row);
        return allocation == null ? "not allocated" : allocation.companyName;
    }),
    textColumn<Analysers.ReferralReportItem>("service group", row => {
        const allocation = getServiceAllocation(row);
        return allocation == null ? "not allocated" : allocation.serviceGroupName;
    }),
    textColumn<Analysers.ReferralReportItem>("client group", row => {
        const allocation = getServiceAllocation(row);
        return allocation == null ? "not allocated" : allocation.clientGroupName;
    }),
    textColumn<Analysers.ReferralReportItem>(
        "project",
        (row, ctx) =>
            ctx.getSessionData().getServiceCategorisation(row.referral.serviceAllocationId)
                .projectName
    ),
    textColumn<Analysers.ReferralReportItem>(
        "region",
        (row, ctx) =>
            ctx
                .getSessionData()
                .getProject(
                    ctx.getSessionData().getServiceCategorisation(row.referral.serviceAllocationId)
                        .projectId || null
                )?.regionName
    ),
    textColumn<Analysers.ReferralReportItem>("from", row => row.referral.source), // NB this includes the agency name, if available
    // referralAgencyId, referralIndividualId
    dateColumn<Analysers.ReferralReportItem>("received", row =>
        EccoDate.parseIso8601(row.referral.receivedDate || null)
    ),
    textColumn<Analysers.ReferralReportItem>("area", row =>
        listDefIdLookup(row.referral.srcGeographicAreaId || null, row.sessionData!)
    ),
    textColumn<Analysers.ReferralReportItem>("srcGeographicAreaName", row =>
        listDefIdLookup(row.referral.srcGeographicAreaId || null, row.sessionData!)
    ),
    dateColumn<Analysers.ReferralReportItem>("data protection", row =>
        EccoDate.parseIso8601FromDateTime(row.referral.dataProtectionAgreementDate || null)
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "data protection status",
        row => row.referral.dataProtectionAgreementStatus || null
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "data protection signed",
        row => !!row.referral.dataProtectionSignedId
    ),
    dateColumn<Analysers.ReferralReportItem>("consent", row =>
        EccoDate.parseIso8601FromDateTime(row.referral.consentAgreementDate || null)
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "consent status",
        row => row.referral.consentAgreementStatus || null
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "consent signed",
        row => !!row.referral.consentSignedId
    ),
    dateColumn<Analysers.ReferralReportItem>("agreement 1", row =>
        EccoDate.parseIso8601FromDateTime(row.referral.agreement1AgreementDate || null)
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "agreement 1 status",
        row => row.referral.agreement1AgreementStatus || null
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "agreement 1 signed",
        row => !!row.referral.agreement1SignedId
    ),
    dateColumn<Analysers.ReferralReportItem>("agreement 2", row =>
        EccoDate.parseIso8601FromDateTime(row.referral.agreement2AgreementDate || null)
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "agreement 2 status",
        row => row.referral.agreement2AgreementStatus || null
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "agreement 2 signed",
        row => !!row.referral.agreement2SignedId
    ),
    dateColumn<Analysers.ReferralReportItem>("agreement 3", row =>
        EccoDate.parseIso8601FromDateTime(row.referral.agreement3AgreementDate || null)
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "agreement 3 status",
        row => row.referral.agreement3AgreementStatus || null
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "agreement 3 signed",
        row => !!row.referral.agreement3SignedId
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "agreement 4 status",
        row => row.referral.agreement4AgreementStatus || null
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "agreement 4 signed",
        row => !!row.referral.agreement4SignedId
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "agreement 5 status",
        row => row.referral.agreement5AgreementStatus || null
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "agreement 5 signed",
        row => !!row.referral.agreement5SignedId
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "agreement 6 status",
        row => row.referral.agreement6AgreementStatus || null
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "agreement 6 signed",
        row => !!row.referral.agreement6SignedId
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "agreement 7 status",
        row => row.referral.agreement7AgreementStatus || null
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "agreement 7 signed",
        row => !!row.referral.agreement7SignedId
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "agreement 8 status",
        row => row.referral.agreement8AgreementStatus || null
    ),
    booleanColumn<Analysers.ReferralReportItem>(
        "agreement 8 signed",
        row => !!row.referral.agreement8SignedId
    ),
    // NB firstContact is no longer editable, and less useful as a result
    dateColumn<Analysers.ReferralReportItem>("first contact", row =>
        EccoDate.parseIso8601(row.referral.firstResponseMadeOn || null)
    ),
    dateTimeColumn<Analysers.ReferralReportItem>("interview (offered)", row =>
        EccoDateTime.parseIso8601(row.referral.firstOfferedInterviewDate || null)
    ),
    dateTimeColumn<Analysers.ReferralReportItem>("interview", row =>
        EccoDateTime.parseIso8601(row.referral.decisionDate || null)
    ),
    numberColumn<Analysers.ReferralReportItem>("dna", row => row.referral.interviewDna),
    dateColumn<Analysers.ReferralReportItem>("decided", row =>
        EccoDate.parseIso8601(row.referral.decisionMadeOn || null)
    ),
    dateColumn<Analysers.ReferralReportItem>("appropriate", row =>
        EccoDate.parseIso8601(row.referral.decisionReferralMadeOn || null)
    ),
    dateColumn<Analysers.ReferralReportItem>("start", row =>
        EccoDate.parseIso8601(row.referral.receivingServiceDate || null)
    ),
    dateColumn<Analysers.ReferralReportItem>("exited", row =>
        EccoDate.parseIso8601(row.referral.exitedDate || null)
    ),
    textColumn<Analysers.ReferralReportItem>("exit reason", row => row.referral.exitReason),
    textColumn<Analysers.ReferralReportItem>("exit comment", row => row.referral.exitComment),
    fixedPrecisionNumberColumn<Analysers.ReferralReportItem>("length (days)", 0, row =>
        lengthOfDaysOnService(row)
    ),
    // NB firstContact is no longer editable, and less useful as a result
    fixedPrecisionNumberColumn<Analysers.ReferralReportItem>("received to first contact", 0, row =>
        daysBetween(row.referral.receivedDate || null, row.referral.firstResponseMadeOn || null)
    ),
    fixedPrecisionNumberColumn<Analysers.ReferralReportItem>(
        "received to interview (offered)",
        0,
        row =>
            daysBetween(
                row.referral.receivedDate || null,
                row.referral.firstOfferedInterviewDate || null
            )
    ),
    fixedPrecisionNumberColumn<Analysers.ReferralReportItem>("received to interview", 0, row =>
        daysBetween(row.referral.receivedDate || null, row.referral.decisionDate || null)
    ),
    fixedPrecisionNumberColumn<Analysers.ReferralReportItem>("received to decided", 0, row =>
        daysBetween(row.referral.receivedDate || null, row.referral.decisionMadeOn || null)
    ),
    fixedPrecisionNumberColumn<Analysers.ReferralReportItem>("decided to start", 0, row =>
        daysBetween(row.referral.decisionMadeOn || null, row.referral.receivingServiceDate || null)
    ), // accepted to start
    fixedPrecisionNumberColumn<Analysers.ReferralReportItem>("received to start", 0, row =>
        daysBetween(row.referral.receivedDate || null, row.referral.receivingServiceDate || null)
    ),
    fixedPrecisionNumberColumn<Analysers.ReferralReportItem>(
        "received to first quest. work",
        0,
        row => daysBetween(row.referral.receivedDate || null, firstWorkDate(row.questionnaireWork!))
    ),
    fixedPrecisionNumberColumn<Analysers.ReferralReportItem>("needs to support", 0, row =>
        daysBetween(
            firstWorkDate(row.supportWork!, "needsAssessment"),
            firstWorkDate(row.supportWork!, "needsReduction")
        )
    ),
    dateColumn<Analysers.ReferralReportItem>("signposted", row =>
        EccoDate.parseIso8601(signpostedDate(row))
    ),
    textColumn<Analysers.ReferralReportItem>("signposted at", row =>
        row.referral.acceptOnServiceState == "SIGNPOSTED"
            ? "accept on service"
            : row.referral.appropriateReferralState == "SIGNPOSTED"
            ? "appropriate referral"
            : null
    ),
    textColumn<Analysers.ReferralReportItem>(
        "signposted reason",
        row => row.referral.signpostedReason
    ),
    textColumn<Analysers.ReferralReportItem>("signposted to", row =>
        row.referral.signpostedBack ? "referrer" : row.referral.signpostedAgencyName
    ),
    textColumn<Analysers.ReferralReportItem>(
        "signposted comment",
        row => row.referral.signpostedComment
    ),
    textColumn<Analysers.ReferralReportItem>("status", row =>
        messageLookup(row.referral.statusMessageKey)
    ),
    textColumn<Analysers.ReferralReportItem>("status now", row =>
        messageLookup(row.referral.statusMessageKey)
    ),
    textColumn<Analysers.ReferralReportItem>(
        "worker",
        row => row.referral.supportWorkerDisplayName
    ),
    textColumn<Analysers.ReferralReportItem>(
        "interviewer",
        row => row.referral.interviewer1WorkerDisplayName
    ),
    textColumn<Analysers.ReferralReportItem>("icd10", row =>
        listDefIdLookup(
            row.referral.choicesMap && row.referral.choicesMap["icd10"]
                ? row.referral.choicesMap["icd10"].id
                : null,
            row.sessionData!
        )
    ),
    textColumn<Analysers.ReferralReportItem>("level", row =>
        listDefIdLookup(
            row.referral.choicesMap && row.referral.choicesMap["supportTier"]
                ? row.referral.choicesMap["supportTier"].id
                : null,
            row.sessionData!
        )
    ),

    // These aren't what was needed
    //        numberColumn<Analysers.ReferralReportItem>("attending mon", (row) => (row.referral.daysAttending & dto.DAYS_AS_BITS.MON) ? 1 : 0),
    //        numberColumn<Analysers.ReferralReportItem>("attending tue", (row) => (row.referral.daysAttending & dto.DAYS_AS_BITS.TUE) ? 1 : 0),
    //        numberColumn<Analysers.ReferralReportItem>("attending wed", (row) => (row.referral.daysAttending & dto.DAYS_AS_BITS.WED) ? 1 : 0),
    //        numberColumn<Analysers.ReferralReportItem>("attending thu", (row) => (row.referral.daysAttending & dto.DAYS_AS_BITS.THUR) ? 1 : 0),
    //        numberColumn<Analysers.ReferralReportItem>("attending fri", (row) => (row.referral.daysAttending & dto.DAYS_AS_BITS.FRI) ? 1 : 0),
    //        numberColumn<Analysers.ReferralReportItem>("attending sat", (row) => (row.referral.daysAttending & dto.DAYS_AS_BITS.SAT) ? 1 : 0),
    //        numberColumn<Analysers.ReferralReportItem>("attending sun", (row) => (row.referral.daysAttending & dto.DAYS_AS_BITS.SUN) ? 1 : 0),
    //        numberColumn<Analysers.ReferralReportItem>("days attending count", (row) => countBits(row.referral.daysAttending)),

    // requires activityInvolvement to be retrieved
    // filter to all those which were on sunday, monday etc including register
    numberColumn<Analysers.ReferralReportItem>("attended sun count", row =>
        row.groupActivities!.reduce(
            (total, involvement) => total + attendancesOnDay(involvement, 0),
            0
        )
    ),
    numberColumn<Analysers.ReferralReportItem>("attended mon count", row =>
        row.groupActivities!.reduce(
            (total, involvement) => total + attendancesOnDay(involvement, 1),
            0
        )
    ),
    numberColumn<Analysers.ReferralReportItem>("attended tue count", row =>
        row.groupActivities!.reduce(
            (total, involvement) => total + attendancesOnDay(involvement, 2),
            0
        )
    ),
    numberColumn<Analysers.ReferralReportItem>("attended wed count", row =>
        row.groupActivities!.reduce(
            (total, involvement) => total + attendancesOnDay(involvement, 3),
            0
        )
    ),
    numberColumn<Analysers.ReferralReportItem>("attended thu count", row =>
        row.groupActivities!.reduce(
            (total, involvement) => total + attendancesOnDay(involvement, 4),
            0
        )
    ),
    numberColumn<Analysers.ReferralReportItem>("attended fri count", row =>
        row.groupActivities!.reduce(
            (total, involvement) => total + attendancesOnDay(involvement, 5),
            0
        )
    ),
    numberColumn<Analysers.ReferralReportItem>("attended sat count", row =>
        row.groupActivities!.reduce(
            (total, involvement) => total + attendancesOnDay(involvement, 6),
            0
        )
    ),

    numberColumn<Analysers.ReferralReportItem>(
        "invited count",
        (
            row // TODO: this doesn't make sense for register style
        ) => row.groupActivities!.size()
    ),
    numberColumn<Analysers.ReferralReportItem>("attending count", row =>
        row.groupActivities!.reduce(
            (total, involvement) => total + expectedAttendances(involvement),
            0
        )
    ),
    numberColumn<Analysers.ReferralReportItem>("attended count", row =>
        row.groupActivities!.reduce(
            (total, involvement) => total + actualAttendances(involvement),
            0
        )
    ),

    // requires supportWork to be retrieved
    numberColumn<Analysers.ReferralReportItem>("achieved", row =>
        achievedSmartSteps(row.supportWork!)
    ),
    numberColumn<Analysers.ReferralReportItem>("outstanding", row =>
        outstandingSmartSteps(row.supportWork!)
    )
);

export function getMonth(date: string | null): number | null {
    if (!date) {
        return null;
    }
    return EccoDate.parseIso8601(date).getMonth();
}

function getAgeInYearsAtTimeOfDeathFromReferralAggregate(
    item: Analysers.ReferralReportItem
): number | null {
    if (!item.client!.birthDate) {
        return null;
    }
    if (!item.client!.dateOfDeath) {
        return null;
    }
    return getAgeInYearsBetweenDates(item.client!.birthDate, item.client!.dateOfDeath);
}
function getAgeInYearsAtTimeOfDeath(item: Client): number | null {
    if (!item.birthDate) {
        return null;
    }
    if (!item.dateOfDeath) {
        return null;
    }
    return getAgeInYearsBetweenDates(item.birthDate, item.dateOfDeath);
}

export let serviceRecipientColumns = columnMap(
    //hrefColumn<ServiceRecipient>("sr-id", (row) => referralHref(row)),
    numberColumn<ServiceRecipient>("sr-id", row => row.serviceRecipientId),
    numberColumn<ServiceRecipient>("serviceRecipientId", row => row.serviceRecipientId),
    textColumn<ServiceRecipient>("name", row => row.displayName)
);

export var clientReportItemColumns = columnMap(
    // requires clientDetail to be retrieved
    textColumn<Analysers.ReferralReportItem>("title", row => row.client?.title),
    textColumn<Analysers.ReferralReportItem>("first name", row => row.client?.firstName),
    textColumn<Analysers.ReferralReportItem>("last name", row => row.client?.lastName),
    textColumn<Analysers.ReferralReportItem>("full address", row =>
        fullAddress(row.client?.address)
    ),
    textColumn<Analysers.ReferralReportItem>(
        "external sys",
        row => row.client?.externalSystemSource
    ),
    textColumn<Analysers.ReferralReportItem>("external ref", row => row.client?.externalSystemRef),
    textColumn<Analysers.ReferralReportItem>("address", row => streetAddress(row.client?.address)),
    textColumn<Analysers.ReferralReportItem>("town", row =>
        row.client?.address ? row.client.address.town : null
    ),
    textColumn<Analysers.ReferralReportItem>("postcode", row =>
        row.client?.address ? row.client.address.postcode : null
    ),
    textColumn<Analysers.ReferralReportItem>("phone", row => row.client?.phoneNumber),
    textColumn<Analysers.ReferralReportItem>("mobile", row => row.client?.mobileNumber),
    textColumn<Analysers.ReferralReportItem>("email", row => row.client?.email),
    dateColumn<Analysers.ReferralReportItem>("birthdate", row =>
        EccoDate.parseIso8601(row.client?.birthDate || null)
    ),
    dateColumn<Analysers.ReferralReportItem>("date of death", row =>
        EccoDate.parseIso8601(row.client?.dateOfDeath || null)
    ),
    numberColumn<Analysers.ReferralReportItem>("date of death month", row =>
        getMonth(row.client?.dateOfDeath || null)
    ),
    numberColumn<Analysers.ReferralReportItem>("age at death", row =>
        getAgeInYearsAtTimeOfDeathFromReferralAggregate(row)
    ),
    textColumn<Analysers.ReferralReportItem>("disability", (row, ctx) => {
        return row.client?.disabilityId == null
            ? "not allocated"
            : ctx
                  .getSessionData()
                  .getListDefinitionEntryById(row.client.disabilityId)
                  .getDisplayName();
    }),
    textColumn<Analysers.ReferralReportItem>("first lang.", (row, ctx) => {
        return row.client?.firstLanguageId == null
            ? "not allocated"
            : ctx
                  .getSessionData()
                  .getListDefinitionEntryById(row.client.firstLanguageId)
                  .getDisplayName();
    }),
    textColumn<Analysers.ReferralReportItem>("sex. orient.", (row, ctx) => {
        return row.client?.sexualOrientationId == null
            ? "not allocated"
            : ctx
                  .getSessionData()
                  .getListDefinitionEntryById(row.client.sexualOrientationId)
                  .getDisplayName();
    }),
    textColumn<Analysers.ReferralReportItem>("religion", (row, ctx) => {
        return row.client?.religionId == null
            ? "not allocated"
            : ctx
                  .getSessionData()
                  .getListDefinitionEntryById(row.client.religionId)
                  .getDisplayName();
    }),
    textColumn<Analysers.ReferralReportItem>("gender", (row, ctx) => {
        return row.client?.genderId == null
            ? "not allocated"
            : ctx.getSessionData().getListDefinitionEntryById(row.client.genderId).getDisplayName();
    }),
    textColumn<Analysers.ReferralReportItem>("ethnicity", (row, ctx) => {
        return row.client?.ethnicOriginId == null
            ? "not allocated"
            : ctx
                  .getSessionData()
                  .getListDefinitionEntryById(row.client.ethnicOriginId)
                  .getDisplayName();
    }),
    numberColumn<Analysers.ReferralReportItem>(
        "age at ref.",
        row => getAgeInYearsAtTimeOfReferral(row),
        true
    ),
    numberColumn<Analysers.ReferralReportItem>("age now", row =>
        getAgeInYearsBetweenDates(
            row.client?.birthDate || null,
            EccoDate.todayLocalTime().formatIso8601()
        )
    ),
    numberColumn<Analysers.ReferralReportItem>(
        "age at exit",
        row => getAgeInYearsAtTimeOfExit(row),
        true
    )
);

export let referralSummaryColumns = columnMap(
    hrefColumn<ReferralSummaryDto>("r-id", row => referralHref(row)),
    numberColumn<ReferralSummaryDto>("referralId", row => row.referralId),
    textColumn<ReferralSummaryDto>("r-code", row => row.referralCode),
    textColumn<ReferralSummaryDto>("c-id", row => row.clientCode || row.clientId.toString()),
    numberColumn<ReferralSummaryDto>("clientId", row => row.clientId),
    textColumn<ReferralSummaryDto>("c-code", row => row.clientCode),
    textColumn<ReferralSummaryDto>("name", row => row.displayName),
    textColumn<ReferralSummaryDto>("status now", row => messageLookup(row.statusMessageKey)),
    //textColumn<ReferralSummaryDto>("worker", (row, ctx) => ctx.getSessionData()..lookup("supportWorkerId")),
    dateColumn<ReferralSummaryDto>("received", row =>
        EccoDate.parseIso8601(row.receivedDate || null)
    ),
    dateColumn<ReferralSummaryDto>("start", row =>
        EccoDate.parseIso8601(row.receivingServiceDate || null)
    ),
    textColumn<ReferralSummaryDto>("company", (row, ctx) => {
        return row.serviceAllocationId == null
            ? "not allocated"
            : ctx.getSessionData().getServiceCategorisation(row.serviceAllocationId).companyName;
    }),
    textColumn<ReferralSummaryDto>("service", (row, ctx) => {
        return row.serviceAllocationId == null
            ? "not allocated"
            : ctx.getSessionData().getServiceCategorisation(row.serviceAllocationId).serviceName;
    }),
    textColumn<ReferralSummaryDto>("project", (row, ctx) => {
        return row.serviceAllocationId == null
            ? "not allocated"
            : ctx.getSessionData().getServiceCategorisation(row.serviceAllocationId).projectName;
    }),
    textColumn<ReferralSummaryDto>("worker", row => row.supportWorkerDisplayName)
);

// TODO remove duplication of mapping here, and above with ReferralReportItem
export var clientOnlyColumns = columnMap(
    textColumn<Client>("c-id", row => row.code || row.clientId?.toString()),
    numberColumn<Client>("clientId", row => row.clientId),
    textColumn<Client>("c-code", row => row.code),
    textColumn<Client>("external sys", row => row.externalSystemSource),
    textColumn<Client>("external ref", row => row.externalSystemRef),
    textColumn<Client>("title", row => row.title),
    textColumn<Client>("first name", row => row.firstName),
    textColumn<Client>("last name", row => row.lastName),
    textColumn<Client>("full address", row => fullAddress(row.address)),
    textColumn<Client>("external ref", row => row.externalSystemRef),
    textColumn<Client>("address", row => streetAddress(row.address)),
    textColumn<Client>("town", row => (row.address ? row.address.town : null)),
    textColumn<Client>("postcode", row => (row.address ? row.address.postcode : null)),
    textColumn<Client>("phone", row => row.phoneNumber),
    textColumn<Client>("mobile", row => row.mobileNumber),
    textColumn<Client>("email", row => row.email),
    dateColumn<Client>("birthdate", row => EccoDate.parseIso8601(row.birthDate || null)),
    dateColumn<Client>("date of death", row => EccoDate.parseIso8601(row.dateOfDeath || null)),
    numberColumn<Client>("date of death month", row => getMonth(row.dateOfDeath || null)),
    numberColumn<Client>("age at death", row => getAgeInYearsAtTimeOfDeath(row)),
    textColumn<Client>("disability", (row, ctx) => {
        return row.disabilityId == null
            ? "not allocated"
            : ctx.getSessionData().getListDefinitionEntryById(row.disabilityId).getDisplayName();
    }),
    textColumn<Client>("first lang.", (row, ctx) => {
        return row.firstLanguageId == null
            ? "not allocated"
            : ctx.getSessionData().getListDefinitionEntryById(row.firstLanguageId).getDisplayName();
    }),
    textColumn<Client>("sex. orient.", (row, ctx) => {
        return row.sexualOrientationId == null
            ? "not allocated"
            : ctx
                  .getSessionData()
                  .getListDefinitionEntryById(row.sexualOrientationId)
                  .getDisplayName();
    }),
    textColumn<Client>("religion", (row, ctx) => {
        return row.religionId == null
            ? "not allocated"
            : ctx.getSessionData().getListDefinitionEntryById(row.religionId).getDisplayName();
    }),
    textColumn<Client>("gender", (row, ctx) => {
        return row.genderId == null
            ? "not allocated"
            : ctx.getSessionData().getListDefinitionEntryById(row.genderId).getDisplayName();
    }),
    textColumn<Client>("ethnicity", (row, ctx) => {
        return row.ethnicOriginId == null
            ? "not allocated"
            : ctx.getSessionData().getListDefinitionEntryById(row.ethnicOriginId).getDisplayName();
    }),
    textColumn<Client>("marital status", (row, ctx) => {
        return row.maritalStatusId == null
            ? "not allocated"
            : ctx.getSessionData().getListDefinitionEntryById(row.maritalStatusId).getDisplayName();
    }),
    textColumn<Client>("pronouns", (row, ctx) => {
        return row.pronounsId == null
            ? "not allocated"
            : ctx.getSessionData().getListDefinitionEntryById(row.pronounsId).getDisplayName();
    }),
    textColumn<Client>("known as", row => row.knownAs),
    textColumn<Client>("ni", row => row.ni),
    textColumn<Client>("communication needs", row => row.communicationNeeds),
    textColumn<Client>("description", row => row.description),
    textColumn<Client>("doctor details", row => row.doctorDetails),
    textColumn<Client>("dentist details", row => row.dentistDetails),
    textColumn<Client>("emergency keycode", row => row.emergencyKeyCode),
    textColumn<Client>("emergency keyword", row => row.emergencyKeyWord),
    textColumn<Client>("emergency details", row => row.emergencyDetails),
    textColumn<Client>("risks and concerns", row => row.risksAndConcerns),
    textColumn<Client>("medication details", row => row.medicationDetails),
    textColumn<Client>("preferred contact", row => row.preferredContactInfo),
    dateTimeColumn<Client>("complete at", row => EccoDateTime.parseIso8601(row.completeAt || null))
);

export var workerColumns = columnMap(
    numberColumn<StaffDto>("w-id", row => row.workerId),
    textColumn<StaffDto>("displayName", row => row.displayName)
);

export var agencyOnlyColumns = columnMap(
    numberColumn<Agency>("contactId", row => row.contactId),
    textColumn<Agency>("company name", row => row.companyName),
    textColumn<Agency>("category", (row, ctx) =>
        listDefIdLookup(row.agencyCategoryId || null, ctx.getSessionData())
    ),
    textColumn<Agency>("full address", row => fullAddress(row.address)),
    textColumn<Agency>("address", row => streetAddress(row.address)),
    textColumn<Agency>("town", row => (row.address ? row.address.town : null)),
    textColumn<Agency>("postcode", row => (row.address ? row.address.postcode : null)),
    textColumn<Agency>("phone", row => row.phoneNumber),
    textColumn<Agency>("email", row => row.email),
    dateColumn<Agency>("archived", row => EccoDate.parseIso8601(row.archived))
);

const preferredContactLookup: StringToStringMap = {
    Unknown: "-",
    e: "email",
    m: "mobile",
    ll: "landline",
    l: "letter",
    s: "sms"
};
export var individualOnlyColumns = columnMap(
    numberColumn<Individual>("contactId", row => row.contactId),
    textColumn<Individual>("job", row => row.jobTitle),
    textColumn<Individual>("first name", row => row.firstName),
    textColumn<Individual>("last name", row => row.lastName),
    textColumn<Individual>("full address", row => fullAddress(row.address)),
    textColumn<Individual>("address", row => streetAddress(row.address)),
    textColumn<Individual>("town", row => (row.address ? row.address.town : null)),
    textColumn<Individual>("postcode", row => (row.address ? row.address.postcode : null)),
    textColumn<Individual>("phone", row => row.phoneNumber),
    textColumn<Individual>("mobile", row => row.mobileNumber),
    textColumn<Individual>("email", row => row.email),
    textColumn<Individual>(
        "preferred",
        row => row.preferredContactMethod && preferredContactLookup[row.preferredContactMethod]
    ),
    dateColumn<Individual>("archived", row => EccoDate.parseIso8601(row.archived))
);

export var individualToAgency = joinNestedPathColumnMaps<Individual, Agency>(
    "a",
    row => row.organisation as Agency,
    agencyOnlyColumns
);
export var professionalColumns = joinColumnMaps(individualOnlyColumns, individualToAgency);

// see EvidenceFormSnapshotToViewModel.java
export const customFormWorkOnlyColumns = columnMap(
    numberColumn<evidenceDto.BaseWork>("sr-id", row => row.serviceRecipientId),
    textColumn<evidenceDto.BaseWork>("uuid", row => row.id),
    dateTimeColumn<evidenceDto.BaseWork>("created", row =>
        EccoDateTime.parseIso8601(row.createdDate)
    ),
    dateTimeColumn<evidenceDto.BaseWork>("work date", row =>
        EccoDateTime.parseIso8601(row.workDate)
    ),
    textColumn<evidenceDto.BaseWork>("task", row =>
        evidenceTaskNameLookup(row.taskName, row.serviceAllocationId)
    ),
    textColumn<evidenceDto.BaseWork>("author", row => row.authorDisplayName)
    // map custom fields using the override idea, eg 'overview' property is:
    // tableRepresentation: { className: ReferralReportItem, columns: [rid, cid, {"title": "client id", "representation": "cid", "path": ["form","overview"]}] }
);

export var referralAggregateToClient = joinNestedPathColumnMaps<
    Analysers.ReferralReportItem,
    Client
>("c", row => row.client, clientOnlyColumns);
const referralAggregateToSourceAgency = joinNestedPathColumnMaps<
    Analysers.ReferralReportItem,
    Agency
>("source agency", row => row.referral.sourceAgency, agencyOnlyColumns);
const referralAggregateToSourceProfessional = joinNestedPathColumnMaps<
    Analysers.ReferralReportItem,
    Individual
>("source professional", row => row.referral.sourceProfessional, individualOnlyColumns);
const referralAggregateToCustomFormLatest = joinNestedPathColumnMaps<
    Analysers.ReferralReportItem,
    FormEvidence<any>
>("f", row => row.customFormWorkLatest, customFormWorkOnlyColumns);
const referralAggregateToServiceRecipientCommandLatest = joinNestedPathColumnMaps<
    Analysers.ReferralReportItem,
    BaseServiceRecipientCommandDto
>("a", row => row.serviceRecipientCommandLatest, commandOnlyColumns);

export var referralReportItemColumns = joinColumnMaps(
    referralAggregateReportItemColumns,
    clientReportItemColumns,
    referralAggregateToClient,
    referralAggregateToCustomFormLatest,
    referralAggregateToSourceAgency,
    referralAggregateToSourceProfessional,
    referralAggregateToServiceRecipientCommandLatest
);

export var referralWithReferrerAgencyReportItemColumns = joinColumnMaps(
    referralAggregateReportItemColumns,
    clientReportItemColumns,
    referralAggregateToSourceAgency,
    referralAggregateToSourceProfessional
);

const referralReportItemRepresentation = representation(referralReportItemColumns, [
    "rid",
    "cid",
    "client",
    "project",
    "from",
    "received",
    "status now",
    "worker",
    "achieved",
    "outstanding"
]);

const referralSummaryRepresentation = representation(referralReportItemColumns, [
    "rid",
    "cid",
    "client"
]);

export var smartStepCountsAnalysisColumns = columnMap(
    textColumn<Analysers.SmartStepCountsAnalysis>("key", row => row.key),
    numberColumn<Analysers.SmartStepCountsAnalysis>("achieved", row => row.totalAchievedSmartSteps),
    numberColumn<Analysers.SmartStepCountsAnalysis>(
        "outstanding",
        row => row.totalOutstandingSmartSteps
    )
);
const smartStepsByKeyRepresentation = representation(smartStepCountsAnalysisColumns, [
    "key",
    "achieved",
    "outstanding"
]);

export var daysOfweekAnalysisColumns = columnMap(
    textColumn<Analysers.DaysOfWeekAnalysis>("activity", row => row.key),
    numberColumn<Analysers.DaysOfWeekAnalysis>("mon", row => row.monday),
    numberColumn<Analysers.DaysOfWeekAnalysis>("tue", row => row.tuesday),
    numberColumn<Analysers.DaysOfWeekAnalysis>("wed", row => row.wednesday),
    numberColumn<Analysers.DaysOfWeekAnalysis>("thur", row => row.thursday),
    numberColumn<Analysers.DaysOfWeekAnalysis>("fri", row => row.friday),
    numberColumn<Analysers.DaysOfWeekAnalysis>("sat", row => row.saturday),
    numberColumn<Analysers.DaysOfWeekAnalysis>("sun", row => row.sunday)
);

const daysAttendingRepresentation = representation(daysOfweekAnalysisColumns, [
    "activity",
    "mon",
    "tue",
    "wed",
    "thur",
    "fri",
    "sat",
    "sun"
]);

export var rotaDemandColumns = columnMap(
    numberColumn<ServiceRecipientDemandDto>("id", row => row.serviceRecipientId),
    textColumn<ServiceRecipientDemandDto>("name", row => row.description),
    numberColumn<ServiceRecipientDemandDto>(
        "agreements count",
        row => row.agreements && row.agreements.length
    ),
    numberColumn<ServiceRecipientDemandDto>(
        "appts count",
        row => row.appointments && row.appointments.length
    ),
    numberColumn<ServiceRecipientDemandDto>(
        "appts (confirmed)",
        row =>
            row.appointments && row.appointments.filter(appt => appt.status == "CONFIRMED").length
    ),
    numberColumn<ServiceRecipientDemandDto>(
        "appts (tentative)",
        row =>
            row.appointments && row.appointments.filter(appt => appt.status == "TENTATIVE").length
    ),
    numberColumn<ServiceRecipientDemandDto>(
        "appts (dropped)",
        row => row.appointments && row.appointments.filter(appt => appt.status == "DROPPED").length
    )
);

function countApptHours(reportAppointments: AppointmentDto[]): number {
    return (
        reportAppointments &&
        reportAppointments.reduce((prev, item) => {
            const duration = EccoDateTime.parseIso8601(item.end)
                .subtractDateTime(EccoDateTime.parseIso8601(item.start))
                .inMinutes();
            return prev + duration;
        }, 0) / 60
    );
}

export var AgreementReportItemColumns = columnMap(
    numberColumn<AgreementDto>("agreement id", row => row.agreementId),
    numberColumn<AgreementDto>("recipient id", row => row.serviceRecipientId),
    textColumn<AgreementDto>("name", row => row.serviceRecipientName),
    dateColumn<AgreementDto>("start date", row => EccoDate.parseIso8601(row.start)),
    dateColumn<AgreementDto>("end date", row => EccoDate.parseIso8601(row.end)),
    numberColumn<AgreementDto>(
        "agreed hours per week",
        row => row.parameters && row.parameters.hoursPerWeek
    )
);

/*
export var rotaScheduleColumns = columnMap(
    numberColumn<DemandScheduleDto>("id", row => row.scheduleId),
    numberColumn<DemandScheduleDto>("sr-id", row => row.serviceRecipientId),
    textColumn<DemandScheduleDto>("name", row => row.serviceRecipientName),
    dateColumn<DemandScheduleDto>("start date", row => EccoDate.parseIso8601(row.start)),
    dateColumn<DemandScheduleDto>("end date", row => EccoDate.parseIso8601(row.end)),
    hrefColumn<DemandScheduleDto>("edit", row => {
        return {
            display: "edit",
            click: () => {
                mountWithServices(
                    Schedule.showInModalEditSchedule(
                        row.serviceRecipientId,
                        row.agreementId,
                        row.scheduleId
                    ),
                    document.createElement("div")
                );
            }
        };
    })
);

// NB only called by unused rotaAppointmentColumns
function showAuditsWithReplay(uuid: string) {
    //CommandHistoryListControl.createModalWithIds(row.serviceRecipientId, null, null);
    // NB assume someone seeing an audit report wasn't permission to see the detail
    const form = CommandHistoryListControl.createWithOneId(uuid, true);
    // we didn't have a header or footer, so just the form
    const $elm = $("<div>");
    $elm.append(form.element());
    showInModalDom("history", $elm[0]);
    form.load();

    // show more audit info
    const $highlight = new ActionButton("show more").clickSynchronous(() => {
        $(".uuid").show();
        AdminMode.bus.fire(new AdminMode(true));
        //$(".uuid:contains('" + uuid + "')").css("background-color", "red");
    });
    $elm.prepend($highlight.element());

    // replay button
}

export var rotaAppointmentColumns = columnMap(
    textColumn<AppointmentDto>("id", row => row.ref),
    numberColumn<AppointmentDto>("sr-id", row => row.serviceRecipientId),
    textColumn<AppointmentDto>("event", row => row.title),
    textColumn<AppointmentDto>("event type", row => row.eventTypeName),
    textColumn<AppointmentDto>("name", row => row.serviceRecipientName),
    dateTimeColumn<AppointmentDto>("start date", row => EccoDateTime.parseIso8601(row.start)),
    dateTimeColumn<AppointmentDto>("end date", row => EccoDateTime.parseIso8601(row.end)),
    textColumn<AppointmentDto>("status", row => row.status),
    textColumn<AppointmentDto>("updated by", row => row.updatedByUri),
    hrefColumn<AppointmentDto>("audit", row => {
        if (!row.updatedByUri) {
            return null;
        }
        return {
            display: "audit",
            click: () => {
                const updatedSplit = row.updatedByUri.split("/");
                const audit = updatedSplit[updatedSplit.length - 1];
                showAuditsWithReplay(audit);
            }
        };
    })
);
*/
