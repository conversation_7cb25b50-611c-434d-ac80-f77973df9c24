import {SignpostedIdFields} from "../referral-dto";
import {AcceptedState} from "../dto";
import {ServiceRecipientStatusFields} from "../service-recipient-dto";
import {Agency} from "../contact-dto";
import {FormEvidence} from "../evidence-dto";

export interface RepairRateDto {
    id: number;
    area: string;
    code: string;
    ref: string;
    description: string;
    unit: string;
    rate: number;
}

export interface RepairDto extends SignpostedIdFields, ServiceRecipientStatusFields {
    repairId: number;
    serviceRecipientId: number;
    buildingId: number;
    displayName: string;
    serviceAllocationId: number;
    statusMessage: string; // statusMessageKey populated from messages
    categoryId: number | null;
    rateId: number | null;
    rateName: string | null;
    priorityId: number | null;

    /** ISO8601 format yyyy-MM-dd */
    receivedDate: string;

    signpostedExitComment: string;
    decisionMadeOn: string;
    acceptOnServiceState: AcceptedState;

    // source
    selfReferral?: boolean;
    referrerAgencyId?: number;
    referrerIndividualId?: number;
    source?: string;
    sourceAgency?: Agency;

    // startOnService
    supportWorkerId: number;
    receivingServiceDate?: string;
    supportWorkerDisplayName: string; // client-only

    // exit
    exitedDate: string;
    exitReasonId: number;
    reviewDate: string;

    // client only
    customFormWorkLatest?: FormEvidence<any> | null;
    // client only
}
