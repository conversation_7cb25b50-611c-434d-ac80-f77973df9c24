/**
 * See com.ecco.webApi.singleValue.SingleValueHistoryViewModel
 */
export interface SingleValueHistoryDto {
    /**
     * The surrogate id.
     */
    id: number;

    /**
     * The service recipient
     */
    serviceRecipientId: number;

    /**
     * The key - typically 'entity.property'
     */
    key: string;

    /**
     * The value of the change that is meaningful to the key.
     * So, this could be a direct value or a reference to an id of another table.
     */
    value: number | null;

    // client-side
    /**
     * The 'reference to an id of another table.' as above. See value.
     * We use this to populate the questionId to lookup the answer value using question choices.
     */
    valueRef?: number;
    // client-side

    /**
     * The date and time when the value applied from
     */
    validFrom: string;

    /**
     * The date and time when the value applied to
     */
    validTo: string | null;
}
