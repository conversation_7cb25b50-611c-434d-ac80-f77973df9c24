package com.ecco.config.service;

import com.ecco.config.config.ConfigConfig;
import com.ecco.config.dom.MenuItem;
import com.ecco.infrastructure.config.root.*;
import com.ecco.test.support.SpringTestSupport;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.assertThat;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(initializers = MenuServiceIntegrationTest.TestAppContextInitializer.class,
    classes={ InfrastructureConfig.class, ConfigConfig.class })
public class MenuServiceIntegrationTest {

    static public class TestAppContextInitializer extends EccoApplicationContextInitializer {
        @Override
        protected void configureEnvironment(ConfigurableEccoEnvironment environment) {
            environment.setActiveProfiles(Profiles.EMBEDDED);
            SpringTestSupport.configureMockPropertySource(environment)
                    .withProperty("liquibase.changelog", "classpath:sql/configDomainChangeLog.xml")
                    .withProperty("db.version", SchemaVersion.LATEST.getAsText())
                    .withProperty("disableAudit", "");
            // use a mysql 'ecco' database (see db.name ConfigurableEccoEnvironment) to see about test failures
            //  NB DROP_CREATE past the missing "setting" error in ECCO-85-extra-setting-columns
            //  since '<createTable tableName="setting">' is in the context 1.1-baseline - see getLiquibaseContexts
            /*
            environment.setActiveProfiles(Profiles.DEV);
            SpringTestSupport.configureMockPropertySource(environment)
                    .withProperty("liquibase", "DROP_CREATE")
                    .withProperty("liquibase.changelog", "classpath:sql/configDomainChangeLog.xml")
                    .withProperty("db.version", SchemaVersion.LATEST.getAsText())
                    .withProperty("disableAudit", "");
            */
        }
    }

    @Autowired
    private MenuService menuService;

    @Test
    public void shouldRetrieveOnlyItemsForEnabledModules() {
        List<MenuItem> menuItems = menuService.getWelcomeMenu().getEnabledMenuItems();

        // Change true to false on hr in liquibaseSchema to test this fails
        assertThat(menuItems.size(), is(1));
    }
}
