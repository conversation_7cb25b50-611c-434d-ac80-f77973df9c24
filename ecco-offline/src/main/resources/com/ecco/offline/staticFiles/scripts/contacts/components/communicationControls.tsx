import $ = require("jquery");
import {CommandQueue} from "ecco-commands";
import {
    apiClient,
    AsyncSessionData,
    CommandForm,
    CommandSubform,
    EccoAPI,
    ModalCommandForm,
    useServicesContext
} from "ecco-components";
import {ErrorBoundary, showReactInModal, textArea} from "ecco-components-core";
import {default as React, FC, useState} from "react";
import {ContactMessageCommand} from "../commands";
import ContactHistory from "./ContactHistory";
import {ServicesContextProvider, mountWithServices} from '../../offline/ServicesContextProvider';
import {TabSelectedEvent} from "../../common/tabEvents";
import {Button} from "@eccosolutions/ecco-mui";
import {SessionDataService} from "../../feature-config/SessionDataService";
import {ContactsAjaxRepository} from "ecco-dto";
import {ValidationErrors} from "../../common/validation";

export function CommunicationPane(props: { contactId: number }) {
    return <>
        <div className="text-right">
            <CommunicationActionButton contactId={props.contactId}/>
        </div>
        <div style={{margin: "8px -8px"}}>
            <ContactHistory contactId={props.contactId}/>
        </div>
    </>;
}


export function showCommunicationInModal(contactId: number) {
    showReactInModal("communication",
        <ErrorBoundary><AsyncSessionData promiseFn={SessionDataService.getFeatures}><ServicesContextProvider>
            <CommunicationPane contactId={contactId}/>
        </ServicesContextProvider></AsyncSessionData></ErrorBoundary>

            , {action:"none"})
}

export function mountCommunicationPaneAtElement(elementId: string, contactId: number) {
    mountWithServices(
        <CommunicationPane contactId={contactId}/>,
        document.getElementById(elementId))
}

export const CommunicationActionButton: FC<{contactId: number}> = ({contactId}) => {
    const [show, setShow] = useState(false);

    return <>
        <Button size="small" variant="outlined"
                onClick={() => setShow(true)}>send message</Button>
        {show && <CommunicationModal
            contactId={contactId}
            setShow={(value) => {
                const id = $("a.ui-tabs-anchor").filter(":contains('communication')").attr("id")
                if (id) {
                    TabSelectedEvent.bus(id).fire(new TabSelectedEvent())
                }
                setShow(value)
            }}
        />}
    </>;
};

type LocalProps = { services: EccoAPI, commandForm: CommandForm };

interface ModalProps {
    setShow: (show: boolean) => void
    contactId: number
}

interface Props extends ModalProps {
}

interface State {
    loading: boolean;
    message: string | null
    mobileNumber: string | null;
}

// NB could use getGlobalEccoAPI() ? or just convert to newer approach
const contactsRepository = new ContactsAjaxRepository(apiClient);

export class CommunicationCommandForm extends CommandSubform<Props & LocalProps, State> {

    constructor(props: Props & LocalProps) {
        super(props);
        this.state = {
            loading: true,
            message: "",
            mobileNumber: null
        }
    }

    public override componentDidMount() {
        super.componentDidMount();
        contactsRepository.findOneContact(this.props.contactId).then(contact => {
            this.setState({loading: false, mobileNumber: contact.mobileNumber});
        });
    }

    getErrors(): string[] {
        return []
    }

    emitChangesTo(commandQueue: CommandQueue) {
        // Send a command with startDate indicating the date the change happens
        const cmd = new ContactMessageCommand(this.props.contactId, this.state.message)
        commandQueue.addCommand(cmd)
    }

    override render() {
        if (this.state.loading) {
            return null;
        }
        const valid = new ValidationErrors("");
        valid.requireSms("mobileNumber", this.state.mobileNumber);
        const WarnMessage = <div className="alert alert-warning">invalid phone number</div>;
        return valid.isValid()
                ? textArea<State>("message", "message", state => this.setState(state), this.state)
                : WarnMessage;
    }

}
/* @Exemplar - see usage */
export const CommunicationModal: FC<ModalProps> = props => {
    const eccoAPI = useServicesContext();

    return (
        <ModalCommandForm
            show={true} // i.e. we're a form that's modal when shown
            setShow={props.setShow}
            title="send a message"
            action="send"
            maxWidth="sm"
        >
            {form => <CommunicationCommandForm
                {...props}
                services={eccoAPI}
                commandForm={form}
            />}
        </ModalCommandForm>
    );
};

